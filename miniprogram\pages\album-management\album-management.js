// 获取全局 app 实例（如需用到全局数据可用）
const app = getApp();

// 引入云开发能力
const db = wx.cloud.database(); // 获取数据库实例
import { showToast, showLoading, hideToast, showError } from '../../utils/toast.js';

// 引入新的工具模块
import { migrateAlbumData } from '../../utils/data-migration.js';
import {
  getAllFolders,
  createCustomFolder,
  renameFolder,
  deleteCustomFolder,
  addImageToFolder,
  removeImageFromFolder,
  toggleImageFavorite
} from '../../utils/folder-manager.js';
import {
  moveToTrash,
  restoreFromTrash,
  permanentDelete,
  getTrashImages,
  emptyTrash,
  autoCleanTrash
} from '../../utils/trash-manager.js';

Page({
  data: {
    // 原有数据字段（保持兼容性）
    albumImages: [], // 存储相册图片列表（每项包含 _id、fileID、tempFileURL、bannerOrder、selected）
    maxBannerCount: 5, // 首页最多展示5张图片
    chooseBannerMode: false, // 是否激活首页图片选择模式
    deleteMode: false, // 是否激活批量删除模式
    page: 0, // 当前页码，从0开始
    pageSize: 20, // 每页加载20张图片
    hasMore: true, // 是否还有更多图片可加载
    loading: false, // 是否正在加载中
    isUploading: false, // 是否正在上传图片

    // 新增数据字段
    currentTab: 'all', // 当前标签页：'all'(全部图片) | 'folders'(文件夹)
    folders: [], // 文件夹列表
    currentFolder: null, // 当前选中的文件夹
    selectedImages: [], // 批量选择的图片ID列表
    isSelectionMode: false, // 是否处于批量选择模式
    showFolderDialog: false, // 是否显示文件夹操作对话框
    showMoreMenu: false, // 是否显示右上角更多菜单

    // 时间轴相关
    groupedImages: [], // 按日期分组的图片数据
    showTimeAxis: true, // 是否显示时间轴



    // 文件夹对话框相关
    selectedImageId: null, // 当前操作的图片ID
    selectedFolderIds: [], // 选中的文件夹ID数组（支持多选）
    currentImageFolders: [], // 当前图片所在的文件夹ID数组

    // 拖拽排序相关
    dragStartIndex: -1, // 拖拽开始的索引
    dragCurrentIndex: -1, // 当前拖拽到的索引
    isDragging: false, // 是否正在拖拽
    isSortMode: false, // 是否处于排序模式
    originalOrder: [], // 原始排序（用于取消时恢复）

    // 计算属性
    hasCustomFolders: false // 是否有自定义文件夹
  },

  // 获取文件夹选中状态（用于WXML中的条件渲染）
  getFolderSelectedState: function(folderId) {
    return this.data.selectedFolderIds.includes(folderId);
  },

  // 获取当前图片所在的文件夹ID数组
  getCurrentImageFolders: function(image) {
    const folders = [];

    // 如果图片在回收站，只返回回收站
    if (image.isDeleted) {
      return ['folder_trash'];
    }

    // 检查收藏夹
    if (image.isFavorite) {
      folders.push('folder_favorite');
    }

    // 检查首页展示
    if (image.bannerOrder) {
      folders.push('folder_banner');
    }

    // 检查自定义文件夹
    if (image.folderIds && Array.isArray(image.folderIds)) {
      folders.push(...image.folderIds);
    }

    return folders;
  },

  // 页面加载时自动获取第一页图片
  onLoad: async function() {
    // 执行数据迁移（确保数据结构是最新的）
    await this.initializeData();

    // 加载初始数据
    this.loadAlbumImages(true); // true表示重置分页
    await this.ensureFoldersExist(); // 确保文件夹存在

    // 启动自动清理任务
    this.startAutoCleanup();
  },

  // 初始化数据（执行迁移）
  initializeData: async function() {
    try {
      showLoading(this, '初始化中...');

      // 执行数据迁移
      const migrationResult = await migrateAlbumData();

      if (!migrationResult.success) {
        console.error('数据迁移失败:', migrationResult.error);
        showError(this, '数据初始化失败');
        return;
      }

    } catch (error) {
      console.error('数据初始化异常:', error);
      showError(this, '初始化过程中发生错误');
    } finally {
      hideToast(this);
    }
  },

  // 分页加载图片（根据当前标签页和文件夹）
  loadAlbumImages: function(reset = false) {
    if (this.data.loading || (!this.data.hasMore && !reset)) return;

    const { currentTab, currentFolder } = this.data;

    // 根据不同标签页调用不同的加载方法
    if (currentTab === 'folders' && currentFolder) {
      this.loadFolderImages(reset);
    } else {
      this.loadAllImages(reset);
    }
  },

  // 加载全部图片（按时间轴分组）
  loadAllImages: function(reset = false) {
    if (this.data.loading || (!this.data.hasMore && !reset)) return;
    this.setData({ loading: true });

    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }

    db.collection('album_images')
      .where({
        isDeleted: db.command.neq(true) // 兼容旧数据：未删除或没有isDeleted字段的图片
      })
      .orderBy('createTime', 'desc')
      .skip(page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        this.processImageResults(res.data, reset, page);
      })
      .catch(error => {
        console.error('加载图片失败:', error);
        this.setData({ loading: false });
        showError(this, '加载图片失败');
      });
  },

  // 加载文件夹中的图片
  loadFolderImages: function(reset = false) {
    if (this.data.loading || (!this.data.hasMore && !reset)) return;
    this.setData({ loading: true });

    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }

    const { currentFolder } = this.data;
    let query;

    if (currentFolder.systemType === 'favorite') {
      // 收藏夹：查询收藏的图片
      query = db.collection('album_images').where({
        isFavorite: true,
        isDeleted: db.command.neq(true) // 兼容旧数据
      });
    } else if (currentFolder.systemType === 'banner') {
      // 首页展示：查询有bannerOrder的图片，按bannerOrder排序
      query = db.collection('album_images').where({
        bannerOrder: db.command.neq(null),
        isDeleted: db.command.neq(true) // 兼容旧数据
      });
    } else if (currentFolder.systemType === 'trash') {
      // 回收站：查询已删除的图片
      query = db.collection('album_images').where({
        isDeleted: true
      });
    } else {
      // 自定义文件夹：查询包含该文件夹ID的图片
      query = db.collection('album_images').where({
        folderIds: db.command.in([currentFolder._id]),
        isDeleted: db.command.neq(true) // 兼容旧数据
      });
    }

    // 根据文件夹类型选择排序方式
    if (currentFolder.systemType === 'banner') {
      // 首页展示：按bannerOrder升序排列
      query = query.orderBy('bannerOrder', 'asc');
    } else {
      // 其他文件夹：按创建时间降序排列
      query = query.orderBy('createTime', 'desc');
    }

    query.skip(page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        this.processImageResults(res.data, reset, page);
      })
      .catch(error => {
        console.error('加载文件夹图片失败:', error);
        this.setData({ loading: false });
        showError(this, '加载图片失败');
      });
  },

  // 加载回收站图片
  loadTrashImages: async function(reset = false) {
    if (this.data.loading || (!this.data.hasMore && !reset)) return;
    this.setData({ loading: true });

    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }

    try {
      const result = await getTrashImages(page, this.data.pageSize);
      if (result.success) {
        const albumImages = reset ? result.data : this.data.albumImages.concat(result.data);
        this.setData({
          albumImages,
          hasMore: result.hasMore,
          page: page + 1,
          loading: false
        });

        // 回收站不需要时间轴分组
        this.setData({ groupedImages: [] });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('加载回收站图片失败:', error);
      this.setData({ loading: false });
      showError(this, '加载回收站图片失败');
    }
  },

  // 处理图片查询结果（获取临时链接并分组）
  processImageResults: function(images, reset, page) {
    if (images.length === 0) {
      this.setData({ hasMore: false, loading: false });
      if (reset) {
        this.setData({ groupedImages: [] });
      }
      return;
    }

    const fileList = images.map(item => item.fileID);

    wx.cloud.getTempFileURL({
      fileList: fileList,
      success: urlRes => {
        const urlMap = {};
        urlRes.fileList.forEach(file => {
          urlMap[file.fileID] = file.tempFileURL;
        });

        const newImages = images.map(item => ({
          ...item,
          tempFileURL: urlMap[item.fileID] || '',
          bannerOrder: item.bannerOrder || null,
          isFavorite: item.isFavorite || false,
          folderIds: item.folderIds || [],
          selected: false // 用于批量选择
        }));

        const albumImages = reset ? newImages : this.data.albumImages.concat(newImages);

        this.setData({
          albumImages,
          hasMore: newImages.length === this.data.pageSize,
          page: page + 1,
          loading: false
        });

        // 如果是全部图片标签页，进行时间轴分组
        if (this.data.currentTab === 'all') {
          this.groupImagesByDate();
        }
      },
      fail: (error) => {
        console.error('获取临时链接失败:', error);
        this.setData({ loading: false });
        showError(this, '获取图片链接失败');
      }
    });
  },

  // 页面滚动到底部时自动加载下一页
  onReachBottom: function() {
    this.loadAlbumImages();
  },

  // 按日期分组图片（时间轴功能）
  groupImagesByDate: function() {
    const { albumImages } = this.data;
    const grouped = {};

    albumImages.forEach(image => {
      const date = new Date(image.createTime);
      const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

      if (!grouped[dateKey]) {
        grouped[dateKey] = {
          date: dateKey,
          displayDate: this.formatDisplayDate(date),
          images: []
        };
      }

      grouped[dateKey].images.push(image);
    });

    // 转换为数组并按日期排序
    const groupedArray = Object.values(grouped).sort((a, b) => {
      return new Date(b.date) - new Date(a.date);
    });

    this.setData({ groupedImages: groupedArray });
  },

  // 格式化显示日期
  formatDisplayDate: function(date) {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // 判断是否为今天或昨天
    if (date.toDateString() === today.toDateString()) {
      return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天';
    } else {
      return `${year}年${month}月${day}日`;
    }
  },

  // 标签页切换
  onTabChange: function(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;

    this.setData({
      currentTab: tab,
      currentFolder: null,
      selectedImages: [],
      isSelectionMode: false
    });

    // 重新加载数据
    this.loadAlbumImages(true);
  },

  // 加载文件夹列表
  loadFolders: async function() {
    try {
      const result = await getAllFolders();

      if (result.success) {
        const hasCustomFolders = result.data.some(folder => folder.type === 'custom');

        // 更新文件夹图片数量
        await this.updateAllFolderCounts(result.data);

        this.setData({
          folders: result.data,
          hasCustomFolders
        });
      } else {
        // 如果加载失败，尝试直接查询数据库
        const directResult = await db.collection('album_folders').get();

        if (directResult.data) {
          const hasCustomFolders = directResult.data.some(folder => folder.type === 'custom');
          this.setData({
            folders: directResult.data,
            hasCustomFolders
          });
        }
      }
    } catch (error) {
      console.error('加载文件夹异常:', error);
      // 如果出错，设置空数组
      this.setData({
        folders: [],
        hasCustomFolders: false
      });
    }
  },

  // 选择文件夹
  onSelectFolder: function(e) {
    const folderId = e.currentTarget.dataset.folderId;
    const folder = this.data.folders.find(f => f._id === folderId);

    if (!folder) return;

    this.setData({
      currentFolder: folder,
      selectedImages: [],
      isSelectionMode: false
    });

    // 重新加载该文件夹的图片
    this.loadAlbumImages(true);
  },

  // 返回文件夹列表
  onBackToFolders: function() {
    this.setData({
      currentFolder: null,
      selectedImages: [],
      isSelectionMode: false
    });

    // 重新加载文件夹列表
    this.loadFolders();
  },

  // 删除当前文件夹（从右上角菜单触发）
  onDeleteCurrentFolder: function() {
    const { currentFolder } = this.data;

    if (!currentFolder || currentFolder.type !== 'custom') {
      showError(this, '只能删除自定义文件夹');
      return;
    }

    this.setData({ showMoreMenu: false });

    // 显示删除确认对话框
    wx.showModal({
      title: '删除文件夹',
      content: `确定要删除文件夹"${currentFolder.name}"吗？\n\n⚠️ 此操作不可恢复\n📁 文件夹中的图片不会被删除`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteCustomFolder(currentFolder._id, currentFolder.name);
        }
      }
    });
  },



  // 启动自动清理任务
  startAutoCleanup: async function() {
    try {
      // 执行自动清理（静默执行，不显示提示）
      await autoCleanTrash();
    } catch (error) {
      console.error('自动清理失败:', error);
    }
  },

  // 图片点击事件
  onImageTap: function(e) {
    const image = e.currentTarget.dataset.image;

    if (this.data.isSelectionMode) {
      // 批量选择模式：切换选择状态
      this.toggleImageSelection(image._id);
    } else {
      // 普通模式：预览图片
      this.previewImage(image);
    }
  },

  // 图片长按事件 - 进入多选模式
  onImageLongPress: function(e) {
    // 如果在排序模式下，不响应长按事件
    if (this.data.isSortMode) {
      return;
    }

    const image = e.currentTarget.dataset.image;

    // 进入批量选择模式并选中当前图片
    this.setData({
      isSelectionMode: true,
      selectedImages: [image._id]
    });

    // 更新图片的选中状态
    this.updateImageSelectionState();

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 回收站图片长按事件 - 同样进入多选模式
  onTrashImageLongPress: function(e) {
    this.onImageLongPress(e);
  },

  // 切换图片选择状态
  toggleImageSelection: function(imageId) {
    const { selectedImages } = this.data;
    const index = selectedImages.indexOf(imageId);

    let newSelectedImages;
    if (index > -1) {
      // 取消选择
      newSelectedImages = selectedImages.filter(id => id !== imageId);
    } else {
      // 添加选择
      newSelectedImages = [...selectedImages, imageId];
    }

    this.setData({
      selectedImages: newSelectedImages
    });

    // 更新图片选中状态显示
    this.updateImageSelectionState();

    // 如果没有选中的图片，退出选择模式
    if (newSelectedImages.length === 0) {
      this.setData({ isSelectionMode: false });
    }
  },

  // 更新图片选中状态显示
  updateImageSelectionState: function() {
    const { selectedImages, albumImages, groupedImages } = this.data;

    // 更新 albumImages 中的选中状态
    const updatedAlbumImages = albumImages.map(image => ({
      ...image,
      selected: selectedImages.includes(image._id)
    }));

    // 更新 groupedImages 中的选中状态
    const updatedGroupedImages = groupedImages.map(group => ({
      ...group,
      images: group.images.map(image => ({
        ...image,
        selected: selectedImages.includes(image._id)
      }))
    }));

    this.setData({
      albumImages: updatedAlbumImages,
      groupedImages: updatedGroupedImages
    });
  },

  // 预览图片
  previewImage: function(image) {
    const { albumImages } = this.data;
    const urls = albumImages.map(img => img.tempFileURL).filter(url => url);
    const current = image.tempFileURL;

    wx.previewImage({
      current,
      urls
    });
  },

  // 右上角更多菜单切换
  onToggleMoreMenu: function() {
    this.setData({
      showMoreMenu: !this.data.showMoreMenu
    });
  },

  // 更多菜单状态变化
  onMoreMenuChange: function(e) {
    this.setData({
      showMoreMenu: false
    });
  },

  // 阻止菜单内部点击事件冒泡
  onMenuStopPropagation: function(e) {
    // 阻止事件冒泡，防止点击菜单内容时关闭菜单
  },

  // 菜单操作处理（处理右上角菜单中的单张图片操作）
  onMenuAction: async function(e) {
    const action = e.currentTarget.dataset.action;
    const { selectedImages } = this.data;

    if (selectedImages.length !== 1) {
      showError(this, '请选择一张图片');
      return;
    }

    this.setData({ showMoreMenu: false });

    const imageId = selectedImages[0];
    const image = this.data.albumImages.find(img => img._id === imageId);

    if (!image) {
      showError(this, '图片不存在');
      return;
    }

    try {
      switch (action) {
        case 'favorite':
          await this.toggleFavorite(imageId);
          break;
        case 'move':
          this.showFolderSelector(imageId);
          break;
        case 'banner':
          await this.toggleBanner(image);
          break;
        case 'delete':
          if (this.data.currentFolder && this.data.currentFolder.systemType === 'trash') {
            await this.permanentDeleteImage(imageId);
          } else {
            await this.deleteImage(imageId);
          }
          break;
        case 'restore':
          await this.restoreImage(imageId);
          break;
      }
    } catch (error) {
      console.error('菜单操作失败:', error);
      showError(this, '操作失败');
    }
  },

  // 创建文件夹
  onCreateFolder: function() {
    this.setData({ showMoreMenu: false });

    wx.showModal({
      title: '新建文件夹',
      editable: true,
      placeholderText: '请输入文件夹名称',
      success: async (res) => {
        if (res.confirm && res.content) {
          const result = await createCustomFolder(res.content);
          if (result.success) {
            showToast(this, { message: '创建成功', theme: 'success' });
            this.loadFolders(); // 重新加载文件夹列表
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },



  // 进入排序模式
  onEnterSortMode: function() {
    const { albumImages } = this.data;

    // 保存原始排序
    const originalOrder = albumImages.map(img => ({
      _id: img._id,
      bannerOrder: img.bannerOrder
    }));

    this.setData({
      showMoreMenu: false,
      isSortMode: true,
      originalOrder: originalOrder
    });

    showToast(this, { message: '点击图片上的箭头调整顺序', theme: 'info' });
  },

  // 批量选择
  onBatchSelect: function() {
    this.setData({
      showMoreMenu: false,
      isSelectionMode: true,
      selectedImages: []
    });

    // 更新图片选中状态显示
    this.updateImageSelectionState();
  },

  // 清空回收站
  onEmptyTrash: async function() {
    this.setData({ showMoreMenu: false });

    wx.showModal({
      title: '清空回收站',
      content: '确定要永久删除回收站中的所有图片吗？此操作不可恢复。',
      confirmText: '清空',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          showLoading(this, '清空中...');
          const result = await emptyTrash();
          hideToast(this);

          if (result.success) {
            showToast(this, { message: result.message, theme: 'success' });
            this.loadAlbumImages(true); // 重新加载
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },



  // 切换收藏状态
  toggleFavorite: async function(imageId) {
    showLoading(this, '处理中...');
    const result = await toggleImageFavorite(imageId);
    hideToast(this);

    if (result.success) {
      showToast(this, { message: result.message, theme: 'success' });
      this.loadAlbumImages(true); // 重新加载
      this.loadFolders(); // 更新文件夹数量
    } else {
      showError(this, result.message);
    }
  },

  // 显示文件夹选择器
  showFolderSelector: async function(imageId) {
    try {
      // 强制重新加载文件夹数据
      await this.loadFolders();

      // 检查是否缺少系统文件夹
      const hasSystemFolders = this.data.folders.some(f => f.type === 'system');
      if (!hasSystemFolders || this.data.folders.length === 0) {
        // 直接在这里创建系统文件夹，不依赖其他方法
        const systemFolders = [
          {
            id: 'folder_favorite',
            name: '收藏夹',
            type: 'system',
            systemType: 'favorite',
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          },
          {
            id: 'folder_banner',
            name: '首页展示',
            type: 'system',
            systemType: 'banner',
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          },
          {
            id: 'folder_trash',
            name: '回收站',
            type: 'system',
            systemType: 'trash',
            createTime: new Date(),
            updateTime: new Date(),
            imageCount: 0
          }
        ];

        for (const folder of systemFolders) {
          try {
            // 不在data中包含_id，而是通过doc()指定
            const { id, ...folderData } = folder;
            await db.collection('album_folders').doc(id).set({
              data: folderData
            });
          } catch (error) {
            if (error.errCode !== -502002) { // 不是"文档已存在"错误
              throw error;
            }
          }
        }

        // 重新加载
        await this.loadFolders();
      }

      // 如果还是没有文件夹，设置一个临时的
      if (this.data.folders.length === 0) {
        this.setData({
          folders: [
            {
              _id: 'folder_favorite',
              name: '收藏夹',
              type: 'system',
              systemType: 'favorite',
              imageCount: 0
            },
            {
              _id: 'folder_banner',
              name: '首页展示',
              type: 'system',
              systemType: 'banner',
              imageCount: 0
            }
          ]
        });
      }

    } catch (error) {
      console.error('准备文件夹选择器时出错:', error);
      // 设置默认文件夹
      this.setData({
        folders: [
          {
            _id: 'folder_favorite',
            name: '收藏夹',
            type: 'system',
            systemType: 'favorite',
            imageCount: 0
          }
        ]
      });
    }

    // 获取当前图片的文件夹信息
    let currentImageFolders = [];
    if (imageId) {
      const image = this.data.albumImages.find(img => img._id === imageId);
      if (image) {
        currentImageFolders = this.getCurrentImageFolders(image);
      }
    }

    this.setData({
      showFolderDialog: true,
      selectedImageId: imageId, // 如果是批量操作，这里会是null
      selectedFolderIds: [...currentImageFolders], // 复制当前文件夹状态
      currentImageFolders: currentImageFolders
    });
  },

  // 显示归类对话框（只显示自定义文件夹）
  showClassifyDialog: async function() {
    try {
      // 强制重新加载文件夹数据
      await this.loadFolders();

      // 过滤出自定义文件夹
      const customFolders = this.data.folders.filter(folder => folder.type === 'custom');

      if (customFolders.length === 0) {
        showToast(this, { message: '暂无自定义文件夹，请先创建', theme: 'warning' });
        return;
      }

      this.setData({
        showFolderDialog: true,
        selectedImageId: null, // 批量操作
        selectedFolderIds: [], // 归类时从空开始选择
        currentImageFolders: [],
        folders: customFolders // 只显示自定义文件夹
      });
    } catch (error) {
      console.error('显示归类对话框失败:', error);
      showError(this, '加载文件夹失败');
    }
  },

  // 切换文件夹选择状态（支持多选）
  onToggleDialogFolder: function(e) {
    const folderId = e.currentTarget.dataset.folderId;
    const { selectedFolderIds } = this.data;

    let newSelectedFolderIds;
    if (selectedFolderIds.includes(folderId)) {
      // 取消选择
      newSelectedFolderIds = selectedFolderIds.filter(id => id !== folderId);
    } else {
      // 添加选择
      newSelectedFolderIds = [...selectedFolderIds, folderId];

      // 回收站逻辑检查
      if (folderId === 'folder_trash') {
        // 如果选择回收站，清除其他所有文件夹
        newSelectedFolderIds = ['folder_trash'];
        showToast(this, { message: '回收站不能与其他文件夹同时选择', theme: 'warning' });
      } else if (newSelectedFolderIds.includes('folder_trash')) {
        // 如果已选择回收站，不能再选择其他文件夹
        newSelectedFolderIds = selectedFolderIds; // 保持原状态
        showToast(this, { message: '回收站不能与其他文件夹同时选择', theme: 'warning' });
        return;
      }
    }

    this.setData({ selectedFolderIds: newSelectedFolderIds });
  },

  // 文件夹对话框确认
  onFolderDialogConfirm: async function() {
    const { selectedImageId, selectedImages, selectedFolderIds, currentImageFolders } = this.data;

    this.setData({ showFolderDialog: false });

    showLoading(this, '保存中...');

    try {
      if (selectedImageId) {
        // 单张图片操作
        await this.updateImageFolders(selectedImageId, selectedFolderIds, currentImageFolders);
        showToast(this, { message: '文件夹设置已保存', theme: 'success' });
      } else if (selectedImages && selectedImages.length > 0) {
        // 批量操作
        for (const imageId of selectedImages) {
          // 获取当前图片的文件夹信息
          const image = this.data.albumImages.find(img => img._id === imageId);
          const currentFolders = image ? this.getCurrentImageFolders(image) : [];

          // 只更新自定义文件夹，保持系统文件夹不变
          const systemFolders = currentFolders.filter(id =>
            id === 'folder_favorite' || id === 'folder_banner' || id === 'folder_trash'
          );
          const newFolderIds = [...systemFolders, ...selectedFolderIds];

          await this.updateImageFolders(imageId, newFolderIds, currentFolders);
        }
        showToast(this, { message: `已为${selectedImages.length}张图片设置归类`, theme: 'success' });
        this.onCancelSelection(); // 退出选择模式
      }

      this.loadAlbumImages(true); // 重新加载图片
      this.loadFolders(); // 更新文件夹数量
    } catch (error) {
      console.error('设置文件夹失败:', error);
      showError(this, '设置失败');
    } finally {
      hideToast(this);
    }
  },

  // 文件夹对话框取消
  onFolderDialogCancel: async function() {
    // 恢复完整的文件夹列表
    await this.loadFolders();

    this.setData({
      showFolderDialog: false,
      selectedFolderIds: [],
      currentImageFolders: []
    });
  },

  // 切换首页显示状态
  toggleBanner: async function(image) {
    const { maxBannerCount } = this.data;

    if (image.bannerOrder) {
      // 取消首页显示
      await this.updateBannerOrder(image._id, null);
    } else {
      // 设为首页显示
      // 先检查是否已达到上限
      const bannerCount = this.data.albumImages.filter(img => img.bannerOrder).length;
      if (bannerCount >= maxBannerCount) {
        showError(this, `最多只能选择${maxBannerCount}张首页图片`);
        return;
      }

      // 分配新的编号
      const newOrder = bannerCount + 1;
      await this.updateBannerOrder(image._id, newOrder);
    }
  },

  // 更新首页显示编号
  updateBannerOrder: async function(imageId, bannerOrder) {
    showLoading(this, '保存中...');

    try {
      await db.collection('album_images').doc(imageId).update({
        data: { bannerOrder }
      });

      hideToast(this);
      showToast(this, { message: '设置成功', theme: 'success' });
      this.loadAlbumImages(true); // 重新加载
      this.loadFolders(); // 更新首页展示文件夹数量
    } catch (error) {
      hideToast(this);
      showError(this, '设置失败');
    }
  },

  // 删除图片（移入回收站）
  deleteImage: async function(imageId) {
    // 确保回收站文件夹存在
    await this.ensureTrashFolderExists();

    showLoading(this, '删除中...');
    const result = await moveToTrash(imageId);
    hideToast(this);

    if (result.success) {
      showToast(this, { message: result.message, theme: 'success' });
      this.loadAlbumImages(true); // 重新加载
      this.loadFolders(); // 更新文件夹数量
    } else {
      showError(this, result.message);
    }
  },

  // 永久删除图片
  permanentDeleteImage: async function(imageId) {
    wx.showModal({
      title: '永久删除',
      content: '确定要永久删除这张图片吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          showLoading(this, '删除中...');
          const result = await permanentDelete(imageId);
          hideToast(this);

          if (result.success) {
            showToast(this, { message: result.message, theme: 'success' });
            this.loadAlbumImages(true); // 重新加载
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },

  // 恢复图片
  restoreImage: async function(imageId) {
    showLoading(this, '恢复中...');
    const result = await restoreFromTrash(imageId);
    hideToast(this);

    if (result.success) {
      showToast(this, { message: result.message, theme: 'success' });
      this.loadAlbumImages(true); // 重新加载
      this.loadFolders(); // 更新文件夹数量
    } else {
      showError(this, result.message);
    }
  },

  // 取消批量选择
  onCancelSelection: function() {
    this.setData({
      isSelectionMode: false,
      selectedImages: []
    });

    // 更新图片选中状态显示
    this.updateImageSelectionState();
  },

  // 批量操作
  onBatchAction: async function(e) {
    const action = e.currentTarget.dataset.action;
    const { selectedImages } = this.data;

    if (selectedImages.length === 0) {
      showError(this, '请先选择图片');
      return;
    }

    try {
      switch (action) {
        case 'favorite':
          await this.batchToggleFavorite();
          break;
        case 'classify':
          this.showClassifyDialog(); // 批量归类（只显示自定义文件夹）
          break;
        case 'delete':
          await this.batchDelete();
          break;
      }
    } catch (error) {
      console.error('批量操作失败:', error);
      showError(this, '操作失败');
    }
  },

  // 批量切换收藏
  batchToggleFavorite: async function() {
    const { selectedImages } = this.data;
    showLoading(this, '处理中...');

    // 批量切换收藏状态
    const promises = selectedImages.map(imageId => toggleImageFavorite(imageId));
    await Promise.all(promises);

    hideToast(this);
    showToast(this, { message: `已处理${selectedImages.length}张图片`, theme: 'success' });

    this.onCancelSelection(); // 退出选择模式
    this.loadAlbumImages(true); // 重新加载
    this.loadFolders(); // 更新文件夹数量
  },

  // 显示批量文件夹选择器
  showBatchFolderSelector: async function() {
    // 直接调用单张图片的文件夹选择器，传入null表示批量操作
    await this.showFolderSelector(null);
  },

  // 如果需要，创建系统文件夹
  createSystemFoldersIfNeeded: async function() {
    try {
      console.log('开始创建系统文件夹...');

      const systemFolders = [
        {
          id: 'folder_favorite',
          name: '收藏夹',
          type: 'system',
          systemType: 'favorite',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        },
        {
          id: 'folder_banner',
          name: '首页展示',
          type: 'system',
          systemType: 'banner',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        },
        {
          id: 'folder_trash',
          name: '回收站',
          type: 'system',
          systemType: 'trash',
          createTime: new Date(),
          updateTime: new Date(),
          imageCount: 0
        }
      ];

      // 批量创建系统文件夹
      for (const folder of systemFolders) {
        try {
          // 不在data中包含_id，而是通过doc()指定
          const { id, ...folderData } = folder;
          await db.collection('album_folders').doc(id).set({
            data: folderData
          });
          console.log(`创建系统文件夹成功: ${folder.name}`);
        } catch (error) {
          if (error.errCode === -502002) {
            // 文档已存在，跳过
            console.log(`系统文件夹已存在: ${folder.name}`);
          } else {
            console.error(`创建系统文件夹失败: ${folder.name}`, error);
          }
        }
      }

      console.log('系统文件夹创建完成');
    } catch (error) {
      console.error('创建系统文件夹异常:', error);
    }
  },

  // 批量删除
  batchDelete: async function() {
    const { selectedImages, currentFolder } = this.data;

    const isTrash = currentFolder && currentFolder.systemType === 'trash';
    const title = isTrash ? '永久删除' : '删除';
    const content = isTrash
      ? `确定要永久删除选中的${selectedImages.length}张图片吗？此操作不可恢复。`
      : `确定要删除选中的${selectedImages.length}张图片吗？`;

    wx.showModal({
      title,
      content,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: async (res) => {
        if (res.confirm) {
          // 如果不是回收站操作，确保回收站文件夹存在
          if (!isTrash) {
            await this.ensureTrashFolderExists();
          }

          showLoading(this, '删除中...');

          let result;
          if (isTrash) {
            result = await permanentDelete(selectedImages);
          } else {
            result = await moveToTrash(selectedImages);
          }

          hideToast(this);

          if (result.success) {
            showToast(this, { message: result.message, theme: 'success' });
            this.onCancelSelection(); // 退出选择模式
            this.loadAlbumImages(true); // 重新加载
            this.loadFolders(); // 更新文件夹数量
          } else {
            showError(this, result.message);
          }
        }
      }
    });
  },

  // 获取当前用户的相册图片列表（保持兼容性）
  getAlbumImages: function() {
    // 重定向到新的加载方法
    this.loadAlbumImages(true);
  },

  // 激活首页图片选择模式
  onChooseBannerMode: function() {
    this.setData({ chooseBannerMode: true });
  },

  // 退出首页图片选择模式
  onCancelChooseBanner: function() {
    this.setData({ chooseBannerMode: false });
  },

  // 激活删除模式
  onDeleteMode: function() {
    // 进入批量删除模式时，为每张图片加selected字段，初始为false
    let albumImages = this.data.albumImages.map(img => ({ ...img, selected: false }));
    this.setData({ deleteMode: true, albumImages });
  },

  // 选择/取消选中图片（批量删除模式下）
  onSelectImage: function(e) {
    // 获取当前图片索引
    const index = e.currentTarget.dataset.index;
    let albumImages = this.data.albumImages;
    // 切换选中状态
    albumImages[index].selected = !albumImages[index].selected;
    this.setData({ albumImages });
  },

  // 退出批量删除模式并批量删除选中图片
  onCancelDeleteMode: function() {
    // 获取所有被选中的图片id和fileID
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      // 没有选中任何图片，直接退出批量删除模式
      this.setData({ deleteMode: false });
      return;
    }
    // 弹窗确认
    wx.showModal({
      title: '批量删除',
      content: `确定要删除选中的${selected.length}张图片吗？`,
      confirmText: '删除',
      confirmColor: '#E34D59',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 1. 批量删除数据库记录
          const db = wx.cloud.database();
          const batchRemove = selected.map(img => db.collection('album_images').doc(img._id).remove());
          Promise.all(batchRemove).then(() => {
            // 2. 批量删除云存储文件
            const fileList = selected.map(img => img.fileID);
            wx.cloud.deleteFile({
              fileList,
              success: () => {
                showToast(this, { message: '删除成功', theme: 'success' });
                this.getAlbumImages(); // 刷新图片列表
                this.setData({ deleteMode: false });
              },
              fail: () => {
                showError(this, '云存储删除失败');
                this.setData({ deleteMode: false });
              }
            });
          }).catch(() => {
            showError(this, '数据库删除失败');
            this.setData({ deleteMode: false });
          });
        } else {
          // 用户取消，退出批量删除模式
          this.setData({ deleteMode: false });
        }
      }
    });
  },

  // 选择/取消首页图片（仅在选择模式下可用）
  onToggleBanner: function(e) {
    if (!this.data.chooseBannerMode) {
      // 不是选择模式，转为预览图片
      this.onPreviewImage(e);
      return;
    }
    const index = e.currentTarget.dataset.index;
    let { albumImages, maxBannerCount } = this.data;
    let img = albumImages[index];
    const selected = albumImages.filter(i => i.bannerOrder).sort((a, b) => a.bannerOrder - b.bannerOrder);

    if (img.bannerOrder) {
      const oldOrder = img.bannerOrder;
      img.bannerOrder = null;
      albumImages.forEach(i => {
        if (i.bannerOrder && i.bannerOrder > oldOrder) {
          i.bannerOrder--;
        }
      });
    } else {
      if (selected.length >= maxBannerCount) {
        showToast(this, { message: '最多只能选5张首页图片', theme: 'warning' });
        return;
      }
      img.bannerOrder = selected.length + 1;
    }

    showLoading(this, '保存中...');
    db.collection('album_images').doc(img._id).update({
      data: {
        bannerOrder: img.bannerOrder,
        updateTime: new Date()
      },
      success: () => {
        hideToast(this);
        this.loadAlbumImages(true); // 使用新的加载方法
        this.loadFolders(); // 更新文件夹数量
      },
      fail: () => {
        hideToast(this);
        showError(this, '保存失败');
      }
    });
  },

  // 退出首页图片选择模式
  onCancelChooseBanner: function() {
    this.setData({ chooseBannerMode: false });
  },

  // 上传图片
  onUploadImage: function() {
    // 防止重复上传
    if (this.data.isUploading) return;

    wx.chooseImage({
      count: 9, // 一次最多可选9张图片（小程序限制）
      sizeType: ['compressed'], // 使用压缩图片
      sourceType: ['album', 'camera'],
      success: chooseRes => {
        const filePaths = chooseRes.tempFilePaths; // 本地临时路径数组
        if (filePaths.length === 0) return;

        // 设置上传状态
        this.setData({ isUploading: true });

        // 用Promise.all批量上传所有图片
        const uploadTasks = filePaths.map(filePath => {
          // 生成云存储路径，index-images/ 文件夹下，文件名用时间戳+随机数
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substr(2, 9);
          const cloudPath = `index-images/${timestamp}_${randomStr}.jpg`;

          return wx.cloud.uploadFile({
            cloudPath,
            filePath
          }).then(uploadRes => {
            // 上传成功后，将 fileID 存入数据库（包含新字段）
            return db.collection('album_images').add({
              data: {
                fileID: uploadRes.fileID,
                createTime: new Date(),
                updateTime: new Date(),
                isFavorite: false,
                folderIds: [],
                isDeleted: false,
                bannerOrder: null
              }
            });
          });
        });

        // 全部上传完成后刷新图片列表
        Promise.all(uploadTasks).then(() => {
          showToast(this, { message: '上传成功', theme: 'success' });
          this.loadAlbumImages(true); // 重新加载图片列表
        }).catch(error => {
          console.error('图片上传失败:', error);
          showError(this, '有图片上传失败');
        }).finally(() => {
          // 重置上传状态
          this.setData({ isUploading: false });
        });
      },
      fail: () => {
        // 用户取消选择图片
        this.setData({ isUploading: false });
      }
    });
  },

  // 删除图片（带确认弹窗）
  onDeleteImage: function(e) {
    const id = e.currentTarget.dataset.id; // 数据库记录id
    const fileID = e.currentTarget.dataset.fileid; // 云存储fileID
    // 弹窗提示用户确认
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      confirmText: '删除',
      confirmColor: '#E34D59', // 红色
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户点击了“删除”
          // 1. 先删除数据库记录
          db.collection('album_images').doc(id).remove({
            success: () => {
              // 2. 再删除云存储文件
              wx.cloud.deleteFile({
                fileList: [fileID],
                success: () => {
                  showToast(this, { message: '删除成功', theme: 'success' });
                  this.getAlbumImages(); // 刷新图片列表
                },
                fail: () => {
                  showError(this, '云存储删除失败');
                }
              });
            },
            fail: () => {
              showError(this, '数据库删除失败');
            }
          });
        }
        // 用户点击“取消”则不做任何操作
      }
    });
  },

  // 预览图片（仅在非选择/删除模式下可用）
  onPreviewImage: function(e) {
    if (this.data.chooseBannerMode || this.data.deleteMode) return; // 选择或删除模式下不响应
    const index = e.currentTarget.dataset.index;
    const urls = this.data.albumImages.map(item => item.tempFileURL);
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  // 确保文件夹存在
  ensureFoldersExist: async function() {
    try {
      // 先尝试加载文件夹
      await this.loadFolders();

      // 检查是否缺少必要的系统文件夹
      await this.ensureSystemFoldersExist();

      // 如果没有文件夹，创建所有系统文件夹
      if (this.data.folders.length === 0) {
        await this.createSystemFoldersIfNeeded();
        await this.loadFolders();
      }
    } catch (error) {
      console.error('确保文件夹存在时出错:', error);
    }
  },

  // 确保系统文件夹存在（特别是回收站）
  ensureSystemFoldersExist: async function() {
    try {
      const requiredSystemFolders = [
        {
          id: 'folder_favorite',
          name: '收藏夹',
          systemType: 'favorite'
        },
        {
          id: 'folder_banner',
          name: '首页展示',
          systemType: 'banner'
        },
        {
          id: 'folder_trash',
          name: '回收站',
          systemType: 'trash'
        }
      ];

      // 检查每个必需的系统文件夹是否存在
      for (const requiredFolder of requiredSystemFolders) {
        const exists = this.data.folders.some(folder =>
          folder._id === requiredFolder.id && folder.type === 'system'
        );

        if (!exists) {
          console.log(`系统文件夹 ${requiredFolder.name} 不存在，正在创建...`);
          await this.createSingleSystemFolder(requiredFolder);
        }
      }

      // 重新加载文件夹列表
      await this.loadFolders();
    } catch (error) {
      console.error('确保系统文件夹存在时出错:', error);
    }
  },

  // 创建单个系统文件夹
  createSingleSystemFolder: async function(folderConfig) {
    try {
      const folderData = {
        name: folderConfig.name,
        type: 'system',
        systemType: folderConfig.systemType,
        createTime: new Date(),
        updateTime: new Date(),
        imageCount: 0
      };

      await db.collection('album_folders').doc(folderConfig.id).set({
        data: folderData
      });

      console.log(`系统文件夹 ${folderConfig.name} 创建成功`);
    } catch (error) {
      if (error.errCode === -502002) {
        // 文档已存在，这是正常情况
        console.log(`系统文件夹 ${folderConfig.name} 已存在`);
      } else {
        console.error(`创建系统文件夹 ${folderConfig.name} 失败:`, error);
        throw error;
      }
    }
  },

  // 更新所有文件夹的图片数量
  updateAllFolderCounts: async function(folders) {
    try {
      for (const folder of folders) {
        const count = await this.updateSingleFolderCount(folder._id, folder.type, folder.systemType);
        folder.imageCount = count; // 直接更新文件夹对象的数量
      }
    } catch (error) {
      console.error('更新文件夹图片数量失败:', error);
    }
  },

  // 更新单个文件夹的图片数量
  updateSingleFolderCount: async function(folderId, folderType, systemType) {
    try {
      let count = 0;

      if (folderType === 'system') {
        if (systemType === 'favorite') {
          // 收藏夹：统计收藏的图片
          const result = await db.collection('album_images')
            .where({
              isFavorite: true,
              isDeleted: db.command.neq(true) // 兼容旧数据
            })
            .count();
          count = result.total;
        } else if (systemType === 'banner') {
          // 首页展示：统计有bannerOrder的图片
          const result = await db.collection('album_images')
            .where({
              bannerOrder: db.command.neq(null),
              isDeleted: db.command.neq(true) // 兼容旧数据
            })
            .count();
          count = result.total;
        } else if (systemType === 'trash') {
          // 回收站：统计已删除的图片
          const result = await db.collection('album_images')
            .where({
              isDeleted: true
            })
            .count();
          count = result.total;
        }
      } else {
        // 自定义文件夹：统计包含该文件夹ID的图片
        const result = await db.collection('album_images')
          .where({
            folderIds: db.command.in([folderId]),
            isDeleted: db.command.neq(true) // 兼容旧数据
          })
          .count();
        count = result.total;
      }

      // 更新数据库中的文件夹图片数量
      await db.collection('album_folders').doc(folderId).update({
        data: {
          imageCount: count,
          updateTime: new Date()
        }
      });

      return count;
    } catch (error) {
      console.error(`更新文件夹 ${folderId} 图片数量失败:`, error);
      return 0;
    }
  },

  // 确保回收站文件夹存在（在删除操作前调用）
  ensureTrashFolderExists: async function() {
    try {
      // 检查回收站文件夹是否存在
      const trashFolder = await db.collection('album_folders').doc('folder_trash').get();

      if (!trashFolder.data) {
        // 回收站文件夹不存在，创建它
        console.log('回收站文件夹不存在，正在创建...');
        await this.createSingleSystemFolder({
          id: 'folder_trash',
          name: '回收站',
          systemType: 'trash'
        });
        console.log('回收站文件夹创建成功');
      }
    } catch (error) {
      if (error.errCode === -502001) {
        // 文档不存在，创建回收站文件夹
        console.log('回收站文件夹不存在，正在创建...');
        await this.createSingleSystemFolder({
          id: 'folder_trash',
          name: '回收站',
          systemType: 'trash'
        });
        console.log('回收站文件夹创建成功');
      } else {
        console.error('检查回收站文件夹时出错:', error);
      }
    }
  },

  // 向上移动图片
  onMoveUp: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index <= 0) return;

    const albumImages = [...this.data.albumImages];
    // 交换位置
    [albumImages[index], albumImages[index - 1]] = [albumImages[index - 1], albumImages[index]];

    this.setData({ albumImages });

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 向下移动图片
  onMoveDown: function(e) {
    const index = e.currentTarget.dataset.index;
    const { albumImages } = this.data;

    if (index >= albumImages.length - 1) return;

    const newAlbumImages = [...albumImages];
    // 交换位置
    [newAlbumImages[index], newAlbumImages[index + 1]] = [newAlbumImages[index + 1], newAlbumImages[index]];

    this.setData({ albumImages: newAlbumImages });

    // 提供触觉反馈
    wx.vibrateShort();
  },

  // 确认排序
  onConfirmSort: async function() {
    try {
      showLoading(this, '保存排序中...');

      const { albumImages } = this.data;

      // 重新分配bannerOrder
      const updatePromises = albumImages.map((image, index) => {
        const newOrder = index + 1;
        return db.collection('album_images').doc(image._id).update({
          data: { bannerOrder: newOrder }
        });
      });

      await Promise.all(updatePromises);

      hideToast(this);
      showToast(this, { message: '排序已保存', theme: 'success' });

      // 退出排序模式
      this.setData({
        isSortMode: false,
        originalOrder: []
      });

      // 重新加载图片以确保顺序正确
      this.loadAlbumImages(true);
      this.loadFolders(); // 更新文件夹数量

    } catch (error) {
      hideToast(this);
      console.error('保存排序失败:', error);
      showError(this, '保存排序失败');
    }
  },

  // 取消排序
  onCancelSort: function() {
    const { originalOrder } = this.data;

    // 恢复原始排序
    const albumImages = [...this.data.albumImages];
    albumImages.sort((a, b) => {
      const orderA = originalOrder.find(item => item._id === a._id)?.bannerOrder || 0;
      const orderB = originalOrder.find(item => item._id === b._id)?.bannerOrder || 0;
      return orderA - orderB;
    });

    this.setData({
      isSortMode: false,
      originalOrder: [],
      albumImages: albumImages
    });

    showToast(this, { message: '已取消排序', theme: 'info' });
  },

  // 更新首页展示图片的排序（保留原方法以防其他地方使用）
  updateBannerOrder: async function() {
    try {
      showLoading(this, '更新排序中...');

      const { albumImages } = this.data;

      // 重新分配bannerOrder
      const updatePromises = albumImages.map((image, index) => {
        const newOrder = index + 1;
        return db.collection('album_images').doc(image._id).update({
          data: { bannerOrder: newOrder }
        });
      });

      await Promise.all(updatePromises);

      hideToast(this);
      showToast(this, { message: '排序更新成功', theme: 'success' });

      // 重新加载图片以确保顺序正确
      this.loadAlbumImages(true);
      this.loadFolders(); // 更新文件夹数量

    } catch (error) {
      hideToast(this);
      console.error('更新排序失败:', error);
      showError(this, '更新排序失败');
    }
  },

  // 移动数组元素（用于拖拽排序）
  moveArrayElement: function(array, fromIndex, toIndex) {
    const element = array[fromIndex];
    array.splice(fromIndex, 1);
    array.splice(toIndex, 0, element);
    return array;
  },

  // 更新图片的文件夹关联
  updateImageFolders: async function(imageId, newFolderIds, oldFolderIds) {
    try {
      const updateData = {};

      // 处理回收站特殊逻辑
      if (newFolderIds.includes('folder_trash')) {
        // 如果要移入回收站，使用专门的回收站逻辑
        const { moveToTrash } = require('../../utils/trash-manager.js');
        return await moveToTrash(imageId);
      }

      // 处理系统文件夹
      const isFavorite = newFolderIds.includes('folder_favorite');
      const isInBanner = newFolderIds.includes('folder_banner');

      updateData.isFavorite = isFavorite;
      updateData.isDeleted = false; // 确保不在回收站

      // 处理首页展示
      if (isInBanner) {
        // 如果要加入首页展示，需要分配bannerOrder
        if (!oldFolderIds.includes('folder_banner')) {
          // 获取当前最大的bannerOrder
          const bannerImages = this.data.albumImages.filter(img => img.bannerOrder);
          const maxOrder = bannerImages.length > 0 ? Math.max(...bannerImages.map(img => img.bannerOrder)) : 0;
          updateData.bannerOrder = maxOrder + 1;
        }
        // 如果已经在首页展示中，保持原有的bannerOrder
      } else {
        // 如果要从首页展示中移除
        updateData.bannerOrder = null;
      }

      // 处理自定义文件夹
      const customFolderIds = newFolderIds.filter(id =>
        id !== 'folder_favorite' &&
        id !== 'folder_banner' &&
        id !== 'folder_trash'
      );
      updateData.folderIds = customFolderIds;
      updateData.updateTime = new Date();

      // 清理可能存在的回收站相关字段
      if (oldFolderIds.includes('folder_trash')) {
        updateData.deletedTime = db.command.remove();
        updateData.beforeDeleteInfo = db.command.remove();
      }

      // 更新数据库
      await db.collection('album_images').doc(imageId).update({
        data: updateData
      });

      return { success: true };
    } catch (error) {
      console.error('更新图片文件夹失败:', error);
      return { success: false, error };
    }
  },

  // 删除自定义文件夹
  deleteCustomFolder: async function(folderId, folderName) {
    try {
      showLoading(this, '删除中...');

      // 1. 从所有图片中移除该文件夹ID
      const imagesResult = await db.collection('album_images')
        .where({
          folderIds: db.command.in([folderId])
        })
        .get();

      if (imagesResult.data.length > 0) {
        const updatePromises = imagesResult.data.map(image => {
          const newFolderIds = (image.folderIds || []).filter(id => id !== folderId);
          return db.collection('album_images').doc(image._id).update({
            data: {
              folderIds: newFolderIds,
              updateTime: new Date()
            }
          });
        });

        await Promise.all(updatePromises);
      }

      // 2. 删除文件夹文档
      await db.collection('album_folders').doc(folderId).remove();

      hideToast(this);
      showToast(this, {
        message: `文件夹"${folderName}"已删除`,
        theme: 'success'
      });

      // 3. 重新加载文件夹列表
      await this.loadFolders();

      // 4. 如果当前正在查看被删除的文件夹，返回文件夹列表
      if (this.data.currentFolder && this.data.currentFolder._id === folderId) {
        this.onBackToFolders();
      }

    } catch (error) {
      hideToast(this);
      console.error('删除文件夹失败:', error);
      showError(this, '删除文件夹失败');
    }
  }

});