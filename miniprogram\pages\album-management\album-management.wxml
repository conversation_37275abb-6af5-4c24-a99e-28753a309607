<t-toast id="t-toast" />

<!-- 相册管理页面 - 三星相册风格 -->
<view class="album-container">
  <!-- 顶部导航栏（沉浸式设计） -->
  <view class="top-nav">
    <!-- 返回按钮（在文件夹详情时显示） -->
    <view wx:if="{{currentTab === 'folders' && currentFolder}}" class="nav-back" bindtap="onBackToFolders">
      <t-icon name="chevron-left" size="24" color="#333" />
      <text class="nav-title">{{currentFolder.name}}</text>
    </view>

    <!-- 页面标题 -->
    <view wx:else class="nav-title-center">
      <text wx:if="{{currentTab === 'all'}}">相册</text>
      <text wx:elif="{{currentTab === 'folders'}}">文件夹</text>
      <text wx:elif="{{currentTab === 'trash'}}">回收站</text>
    </view>

    <!-- 右上角菜单按钮 -->
    <view class="nav-menu" bindtap="onToggleMoreMenu">
      <t-icon name="more" size="24" color="#333" />
    </view>
  </view>

  <!-- 主内容区域 -->
  <view class="main-content">
    <!-- 全部图片标签页 -->
    <view wx:if="{{currentTab === 'all'}}" class="tab-content">
      <!-- 上传按钮 -->
      <view class="upload-section">
        <view class="upload-btn" bindtap="onUploadImage">
          <t-icon name="add" size="20" color="#0052d9" />
          <text class="upload-btn-text">添加图片</text>
        </view>
        <!-- 上传状态 -->
        <view wx:if="{{isUploading}}" class="upload-status">
          <t-loading size="16" />
          <text class="upload-status-text">上传中...</text>
        </view>
      </view>



      <!-- 时间轴图片展示 -->
      <view wx:if="{{groupedImages.length > 0}}" class="timeline-container">
        <view wx:for="{{groupedImages}}" wx:key="date" class="timeline-group">
          <!-- 日期标签 -->
          <view class="date-label">
            <text class="date-text">{{item.displayDate}}</text>
            <text class="date-count">{{item.images.length}}张</text>
          </view>

          <!-- 该日期的图片网格 -->
          <view class="images-grid">
            <view
              wx:for="{{item.images}}"
              wx:for-item="image"
              wx:for-index="imageIndex"
              wx:key="_id"
              class="image-item {{image.selected ? 'selected' : ''}}"
              data-image="{{image}}"
              bindtap="onImageTap"
              bindlongpress="onImageLongPress"
            >
              <!-- 选择状态复选框 -->
              <view wx:if="{{isSelectionMode}}" class="selection-checkbox">
                <t-icon
                  name="{{image.selected ? 'check-circle-filled' : 'circle'}}"
                  size="20"
                  color="{{image.selected ? '#0052d9' : '#fff'}}"
                />
              </view>

              <!-- 首页展示标识 -->
              <view wx:if="{{image.bannerOrder && !isSelectionMode}}" class="banner-icon">
                <t-icon name="home" size="14" color="#fff" />
              </view>

              <!-- 收藏图标 -->
              <view wx:if="{{image.isFavorite && !isSelectionMode}}" class="favorite-icon">
                <t-icon name="heart-filled" size="16" color="#ff4757" />
              </view>

              <!-- 图片 -->
              <image
                src="{{image.tempFileURL}}"
                mode="aspectFill"
                class="grid-image"
                lazy-load="{{true}}"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view wx:if="{{!loading && groupedImages.length === 0 && albumImages.length === 0}}" class="empty-state">
        <t-icon name="image" size="48" color="#ddd" />
        <text class="empty-text">还没有图片</text>
        <text class="empty-desc">点击上方按钮添加图片</text>
      </view>
    </view>

    <!-- 文件夹标签页 -->
    <view wx:if="{{currentTab === 'folders'}}" class="tab-content">
      <!-- 文件夹列表视图 -->
      <view wx:if="{{!currentFolder}}" class="folders-list">
        <!-- 系统文件夹 -->
        <view class="folder-section">
          <view class="section-title">系统文件夹</view>
          <view
            wx:for="{{folders}}"
            wx:key="_id"
            wx:if="{{item.type === 'system'}}"
            class="folder-item"
            data-folder-id="{{item._id}}"
            bindtap="onSelectFolder"
          >
            <view class="folder-icon">
              <t-icon
                name="{{item.systemType === 'favorite' ? 'heart-filled' : (item.systemType === 'trash' ? 'delete' : 'home')}}"
                size="24"
                color="{{item.systemType === 'favorite' ? '#ff4757' : (item.systemType === 'trash' ? '#666' : '#0052d9')}}"
              />
            </view>
            <view class="folder-info">
              <text class="folder-name">{{item.name}}</text>
              <text class="folder-count">{{item.imageCount}}张</text>
              <text wx:if="{{item.systemType === 'trash' && item.imageCount > 0}}" class="folder-desc">30天后自动清理</text>
            </view>
            <t-icon name="chevron-right" size="16" color="#999" />
          </view>
        </view>

        <!-- 自定义文件夹 -->
        <view class="folder-section" wx:if="{{hasCustomFolders}}">
          <view class="section-title">我的文件夹</view>
          <view
            wx:for="{{folders}}"
            wx:key="_id"
            wx:if="{{item.type === 'custom'}}"
            class="folder-item"
            data-folder-id="{{item._id}}"
            bindtap="onSelectFolder"
          >
            <view class="folder-icon">
              <t-icon name="folder" size="24" color="#ffa502" />
            </view>
            <view class="folder-info">
              <text class="folder-name">{{item.name}}</text>
              <text class="folder-count">{{item.imageCount}}张</text>
            </view>
            <t-icon name="chevron-right" size="16" color="#999" />
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:if="{{folders.length === 0}}" class="empty-state">
          <t-icon name="folder" size="48" color="#ddd" />
          <text class="empty-text">还没有文件夹</text>
          <text class="empty-desc">点击右上角菜单创建文件夹</text>
        </view>
      </view>

      <!-- 文件夹详情视图（显示文件夹中的图片） -->
      <view wx:if="{{currentFolder}}" class="folder-detail">
        <!-- 文件夹信息 -->
        <view class="folder-header">
          <view class="folder-meta">
            <text class="folder-detail-name">{{currentFolder.name}}</text>
            <text class="folder-detail-count">{{albumImages.length}}张图片</text>
            <text wx:if="{{currentFolder.systemType === 'banner'}}" class="folder-tip">
              拖拽图片可调整首页显示顺序
            </text>
          </view>
        </view>

        <!-- 图片网格 -->
        <view wx:if="{{albumImages.length > 0}}" class="images-grid">
          <!-- 首页展示文件夹支持拖拽排序 -->
          <block wx:if="{{currentFolder.systemType === 'banner'}}">
            <view
              wx:for="{{albumImages}}"
              wx:key="_id"
              class="image-item {{item.selected ? 'selected' : ''}} {{isSortMode ? 'sortable' : ''}}"
              data-image="{{item}}"
              data-index="{{index}}"
              bindtap="onImageTap"
              bindlongpress="onImageLongPress"
            >
              <!-- 选择状态复选框 -->
              <view wx:if="{{isSelectionMode}}" class="selection-checkbox">
                <t-icon
                  name="{{item.selected ? 'check-circle-filled' : 'circle'}}"
                  size="20"
                  color="{{item.selected ? '#0052d9' : '#fff'}}"
                />
              </view>

              <!-- 排序控制按钮 -->
              <view wx:if="{{isSortMode}}" class="sort-controls">
                <view class="sort-btn" bindtap="onMoveUp" data-index="{{index}}" wx:if="{{index > 0}}">
                  <t-icon name="chevron-up" size="14" color="#fff" />
                </view>
                <view class="sort-btn" bindtap="onMoveDown" data-index="{{index}}" wx:if="{{index < albumImages.length - 1}}">
                  <t-icon name="chevron-down" size="14" color="#fff" />
                </view>
              </view>

              <!-- 首页展示标识 -->
              <view wx:if="{{item.bannerOrder && !isSelectionMode}}" class="banner-icon">
                <t-icon name="home" size="14" color="#fff" />
              </view>

              <!-- 图片 -->
              <image
                src="{{item.tempFileURL}}"
                mode="aspectFill"
                class="grid-image"
                lazy-load="{{true}}"
              />
            </view>
          </block>

          <!-- 其他文件夹的普通显示 -->
          <block wx:else>
            <view
              wx:for="{{albumImages}}"
              wx:key="_id"
              class="image-item {{item.selected ? 'selected' : ''}}"
              data-image="{{item}}"
              bindtap="onImageTap"
              bindlongpress="onImageLongPress"
            >
              <!-- 选择状态复选框 -->
              <view wx:if="{{isSelectionMode}}" class="selection-checkbox">
                <t-icon
                  name="{{item.selected ? 'check-circle-filled' : 'circle'}}"
                  size="20"
                  color="{{item.selected ? '#0052d9' : '#fff'}}"
                />
              </view>

              <!-- 首页展示标识 -->
              <view wx:if="{{item.bannerOrder && !isSelectionMode}}" class="banner-icon">
                <t-icon name="home" size="14" color="#fff" />
              </view>

              <!-- 收藏图标 -->
              <view wx:if="{{item.isFavorite && !isSelectionMode}}" class="favorite-icon">
                <t-icon name="heart-filled" size="16" color="#ff4757" />
              </view>

              <!-- 图片 -->
              <image
                src="{{item.tempFileURL}}"
                mode="aspectFill"
                class="grid-image"
                lazy-load="{{true}}"
              />
            </view>
          </block>
        </view>

        <!-- 空状态 -->
        <view wx:if="{{!loading && albumImages.length === 0}}" class="empty-state">
          <t-icon name="image" size="48" color="#ddd" />
          <text class="empty-text">文件夹为空</text>
          <text class="empty-desc">长按图片可以移动到此文件夹</text>
        </view>
      </view>
    </view>



    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <t-loading size="24" />
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 底部标签导航 -->
  <view class="bottom-tabs">
    <view
      class="tab-item {{currentTab === 'all' ? 'active' : ''}}"
      data-tab="all"
      bindtap="onTabChange"
    >
      <t-icon name="image" size="20" color="{{currentTab === 'all' ? '#0052d9' : '#999'}}" />
      <text class="tab-text">全部图片</text>
    </view>

    <view
      class="tab-item {{currentTab === 'folders' ? 'active' : ''}}"
      data-tab="folders"
      bindtap="onTabChange"
    >
      <t-icon name="folder" size="20" color="{{currentTab === 'folders' ? '#0052d9' : '#999'}}" />
      <text class="tab-text">文件夹</text>
    </view>
  </view>

  <!-- 批量操作底部栏 -->
  <view wx:if="{{isSelectionMode}}" class="selection-toolbar">
    <view class="selection-toolbar-content">
      <!-- 左侧：选择信息 -->
      <view class="selection-info">
        <text class="count-text">已选择</text>
        <text class="count-number">{{selectedImages.length}}</text>
        <text class="count-text">张</text>
      </view>

      <!-- 右侧：操作按钮 -->
      <view class="selection-actions">
        <view class="action-btn" bindtap="onCancelSelection">
          <t-icon name="close" size="18" color="#666" />
          <text>取消</text>
        </view>
        <view class="action-btn favorite" bindtap="onBatchAction" data-action="favorite">
          <t-icon name="heart" size="18" color="#ff4757" />
          <text>收藏</text>
        </view>
        <view class="action-btn classify" bindtap="onBatchAction" data-action="classify">
          <t-icon name="folder" size="18" color="#0052d9" />
          <text>归类</text>
        </view>
        <view class="action-btn danger" bindtap="onBatchAction" data-action="delete">
          <t-icon name="delete" size="18" color="#ff4757" />
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 排序模式底部栏 -->
  <view wx:if="{{isSortMode}}" class="sort-toolbar">
    <view class="sort-toolbar-content">
      <!-- 左侧：提示信息 -->
      <view class="sort-info">
        <t-icon name="swap" size="18" color="#0052d9" />
        <text class="sort-text">点击箭头调整顺序</text>
      </view>

      <!-- 右侧：操作按钮 -->
      <view class="sort-actions">
        <view class="action-btn" bindtap="onCancelSort">
          <t-icon name="close" size="18" color="#666" />
          <text>取消</text>
        </view>
        <view class="action-btn confirm" bindtap="onConfirmSort">
          <t-icon name="check" size="18" color="#52c41a" />
          <text>确认</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 右上角更多菜单 -->
<view wx:if="{{showMoreMenu}}" class="more-menu-overlay" bindtap="onMoreMenuChange">
  <view class="more-menu" catchtap="onMenuStopPropagation">
    <!-- 文件夹管理 -->
    <view wx:if="{{currentTab === 'folders' && !currentFolder}}" class="menu-item" bindtap="onCreateFolder">
      <t-icon name="add" size="16" />
      <text>新建文件夹</text>
    </view>



    <!-- 首页展示排序 -->
    <view wx:if="{{currentFolder && currentFolder.systemType === 'banner' && !isSelectionMode && !isSortMode && albumImages.length > 1}}" class="menu-item" bindtap="onEnterSortMode">
      <t-icon name="swap" size="16" />
      <text>图片排序</text>
    </view>

    <!-- 批量选择 -->
    <view wx:if="{{!isSelectionMode && !isSortMode && albumImages.length > 0}}" class="menu-item" bindtap="onBatchSelect">
      <t-icon name="check-circle" size="16" />
      <text>批量选择</text>
    </view>

    <!-- 单张图片操作（当选中一张图片时显示） -->
    <view wx:if="{{selectedImages.length === 1}}">
      <view class="menu-divider"></view>
      <view class="menu-section-title">图片操作</view>
      <view class="menu-item" bindtap="onMenuAction" data-action="favorite">
        <t-icon name="heart" size="16" />
        <text>收藏/取消收藏</text>
      </view>
      <view class="menu-item" bindtap="onMenuAction" data-action="move">
        <t-icon name="folder" size="16" />
        <text>移动到文件夹</text>
      </view>
      <view wx:if="{{currentFolder && currentFolder.systemType !== 'trash'}}" class="menu-item" bindtap="onMenuAction" data-action="banner">
        <t-icon name="home" size="16" />
        <text>设为首页图片</text>
      </view>
      <view class="menu-item danger" bindtap="onMenuAction" data-action="delete">
        <t-icon name="delete" size="16" />
        <text>{{currentFolder && currentFolder.systemType === 'trash' ? '永久删除' : '删除'}}</text>
      </view>
      <view wx:if="{{currentFolder && currentFolder.systemType === 'trash'}}" class="menu-item" bindtap="onMenuAction" data-action="restore">
        <t-icon name="refresh" size="16" />
        <text>恢复</text>
      </view>
    </view>

    <!-- 自定义文件夹管理 -->
    <view wx:if="{{currentFolder && currentFolder.type === 'custom'}}">
      <view class="menu-divider"></view>
      <view class="menu-item danger" bindtap="onDeleteCurrentFolder">
        <t-icon name="delete" size="16" />
        <text>删除文件夹</text>
      </view>
    </view>

    <!-- 回收站管理 -->
    <view wx:if="{{currentFolder && currentFolder.systemType === 'trash'}}">
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onEmptyTrash">
        <t-icon name="delete" size="16" />
        <text>清空回收站</text>
      </view>
    </view>
  </view>
</view>



<!-- 文件夹选择对话框 -->
<view wx:if="{{showFolderDialog}}" class="folder-dialog-overlay" bindtap="onFolderDialogCancel">
  <view class="folder-dialog-container" catchtap="onMenuStopPropagation">
    <!-- 标题栏 -->
    <view class="folder-dialog-header">
      <text class="folder-dialog-title">{{selectedImageId ? '管理文件夹' : '选择归类'}}</text>
      <view class="folder-dialog-close" bindtap="onFolderDialogCancel">
        <t-icon name="close" size="20" color="#666" />
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="folder-dialog-content">
      <!-- 文件夹列表 -->
      <view
        wx:for="{{folders}}"
        wx:key="_id"
        class="folder-dialog-item {{getFolderSelectedState(item._id) ? 'selected' : ''}} {{item.systemType === 'trash' ? 'trash-folder' : ''}}"
        data-folder-id="{{item._id}}"
        bindtap="onToggleDialogFolder"
      >
        <t-icon
          name="{{item.type === 'system' ? (item.systemType === 'favorite' ? 'heart-filled' : (item.systemType === 'trash' ? 'delete' : 'home')) : 'folder'}}"
          size="16"
          color="{{item.systemType === 'trash' ? '#ff4757' : ''}}"
        />
        <view class="folder-dialog-info">
          <text class="folder-dialog-name">{{item.name}} ({{item.imageCount || 0}}张)</text>
          <text wx:if="{{item.systemType === 'trash'}}" class="folder-dialog-desc">不能与其他文件夹同时选择</text>
        </view>
        <t-icon
          wx:if="{{getFolderSelectedState(item._id)}}"
          name="check"
          size="16"
          color="#0052d9"
        />
      </view>

      <!-- 空状态提示 -->
      <view wx:if="{{folders.length === 0}}" class="folder-empty-state" style="padding: 40rpx; text-align: center;">
        <text style="color: #666; font-size: 26rpx; display: block;">暂无可用文件夹</text>
        <text style="color: #999; font-size: 22rpx; display: block; margin-top: 10rpx;">请检查控制台日志</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="folder-dialog-footer">
      <t-button size="medium" theme="default" bindtap="onFolderDialogCancel">取消</t-button>
      <t-button size="medium" theme="primary" bindtap="onFolderDialogConfirm">保存</t-button>
    </view>
  </view>
</view>